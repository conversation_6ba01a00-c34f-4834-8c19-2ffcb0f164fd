package com.sanythadmin.project.workstudy.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNodeRecord;
import com.sanythadmin.project.workflow.param.WorkflowApprovalNodeParam;
import com.sanythadmin.project.workstudy.entity.QgzxEmploymentEndApply;
import com.sanythadmin.project.workstudy.param.QgzxEmploymentEndApplyParam;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 用工结束申请Service
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface QgzxEmploymentEndApplyService extends IService<QgzxEmploymentEndApply> {

    PageResult<QgzxEmploymentEndApply> pageList(QgzxEmploymentEndApplyParam param);

    List<QgzxEmploymentEndApply> listApply(QgzxEmploymentEndApplyParam param);

    void edit(QgzxEmploymentEndApply employmentEndApply);

    void applyByEmployer(QgzxEmploymentEndApply employmentEndApply);

    CompletableFuture<String> approve(WorkflowApprovalNodeRecord record, QgzxEmploymentEndApply employmentEndApply, Executor executor);

    void delete(String... ids);

    void checkIsEdit(String id);

    QgzxEmploymentEndApply getByStudentApplyId(String studentApplyId);

    boolean canApplyEmploymentEnd(String studentApplyId);

    PageResult<QgzxEmploymentEndApply> pageApprovalList(QgzxEmploymentEndApplyParam param, UserInfoParam userInfoParam, WorkflowApprovalNodeParam approvalNodeParam);

    List<QgzxEmploymentEndApply> listApprovalList(QgzxEmploymentEndApplyParam param, UserInfoParam userInfoParam, WorkflowApprovalNodeParam approvalNodeParam);

    <T> List<Map<String, Object>> distinct(SFunction<QgzxEmploymentEndApply, T> column);

    /**
     * 管理者直接结束用工
     * @param studentApplyId 学生申请ID
     * @param reason 结束原因
     */
    void endEmploymentByAdmin(String studentApplyId, String reason);
}
