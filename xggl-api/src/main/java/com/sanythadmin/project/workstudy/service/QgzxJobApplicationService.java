package com.sanythadmin.project.workstudy.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNodeRecord;
import com.sanythadmin.project.workflow.param.WorkflowApprovalNodeParam;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.param.QgzxEmployerParam;
import com.sanythadmin.project.workstudy.param.QgzxJobApplicationParam;
import com.sanythadmin.project.workstudy.vo.QgzxJobApplicationVO;
import jakarta.validation.constraints.NotNull;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 岗位申报Service
 *
 * <AUTHOR>
 * @since 2025-07-15 16:32:01
 */
public interface QgzxJobApplicationService extends IService<QgzxJobApplication> {

    String checkIsEdit(String applicationId);

    void edit(QgzxJobApplication jobApplication);

    void editByApplicant(QgzxJobApplication jobApplication);

    void delete(String... ids);

    void deleteByApplicant(String... ids);

    List<QgzxJobApplication> listApprovalList(QgzxJobApplicationParam param,QgzxEmployerParam employerParam,WorkflowApprovalNodeParam approvalNodeParam);

    PageResult<QgzxJobApplication> pageApprovalList(QgzxJobApplicationParam param, QgzxEmployerParam employerParam, WorkflowApprovalNodeParam approvalNodeParam);

    List<QgzxJobApplication> listApplicantList(QgzxJobApplicationParam param,QgzxEmployerParam employerParam);

    PageResult<QgzxJobApplication> pageByApplicantList(QgzxJobApplicationParam param, QgzxEmployerParam employerParam);

    QgzxJobApplication getQgzxJobApplication(String id);

    List<Map<String, Object>> distinct(@NotNull SFunction<QgzxJobApplication, String> field);

    CompletableFuture<String> approve(WorkflowApprovalNodeRecord record, QgzxJobApplication application, Executor executor);

    PageResult<QgzxJobApplicationVO> pageByStudent(QgzxJobApplicationParam param);

    List<QgzxJobApplicationVO> listByStudent(QgzxJobApplicationParam param);

    boolean checkJobCodeExists(String jobCode, String excludeId);

    String generateJobCodeByName(String jobName);
}
