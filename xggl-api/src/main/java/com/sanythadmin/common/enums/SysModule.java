package com.sanythadmin.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import com.sanythadmin.common.core.config.enumConverterFactory.TextBaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SysModule implements TextBaseEnum {
    qgzx("qgzx","勤工助学"),
    qgzxGwsb("qgzx_gwsb","勤工助学岗位申报"),
    qgzxSqgw("qgzx_sqgw","勤工助学申请岗位"),
//    qgzxGwtj("qgzx_gwtj","勤工助学岗位调剂"),
    qgzxYgjs("qgzx_ygjs","勤工助学用工结束"),
    qgzxBcsb("qgzx_bcsb","勤工助学报酬申报")
    ;

    @EnumValue
    private final String code;
    @JsonValue
    private final String name;

    @Override
    public String getText() {
        return name;
    }
}
