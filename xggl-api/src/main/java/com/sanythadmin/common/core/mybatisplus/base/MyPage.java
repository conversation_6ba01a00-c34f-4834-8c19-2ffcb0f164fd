package com.sanythadmin.common.core.mybatisplus.base;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanythadmin.common.core.utils.CommonUtil;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * @since 2025/8/1.
 */
@Getter
@EqualsAndHashCode(callSuper = true)
public class MyPage<T> extends Page<T> {

    protected String distinctCountColumn;

    public MyPage() {
        super();
    }

    public MyPage(long current, long size) {
        super(current, size, 0L);
    }

    public MyPage(long current, long size, long total) {
        super(current, size, total, true);
    }

    public MyPage(long current, long size, boolean searchCount) {
        super(current, size, 0L, searchCount);
    }

    public MyPage(long current, long size, long total, boolean searchCount) {
        super(current, size, total, searchCount);
    }

    public MyPage<T> setDistinctCountColumn(String... column) {
        if (column != null && column.length > 0) {
            String strJoin = CommonUtil.strJoin(column);
            this.distinctCountColumn = StringUtils.hasText(distinctCountColumn) ?
                    distinctCountColumn + "," + strJoin : strJoin;
        }
        return this;
    }
}
