package com.sanythadmin.common.core.mybatisplus.interceptor;

import com.baomidou.mybatisplus.core.plugins.InterceptorIgnoreHelper;
import com.baomidou.mybatisplus.core.toolkit.ExceptionUtils;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.parser.JsqlParserGlobal;
import com.baomidou.mybatisplus.extension.plugins.handler.MultiDataPermissionHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.DataPermissionInterceptor;
import com.sanythadmin.common.core.mybatisplus.handler.CustomDataPermissionHandler;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.SelectBody;
import net.sf.jsqlparser.statement.select.SetOperationList;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.sql.SQLException;
import java.util.List;

/**
 * Created by JIANGPING on 2024/8/21.
 */
public class CustomDataPermissionInterceptor extends DataPermissionInterceptor {
    public CustomDataPermissionInterceptor(CustomDataPermissionHandler customDataPermissionHandler) {
        super(customDataPermissionHandler);
    }

    @Override
    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
        if (!InterceptorIgnoreHelper.willIgnoreDataPermission(ms.getId())) {
            PluginUtils.MPBoundSql mpBs = PluginUtils.mpBoundSql(boundSql);
            mpBs.sql(this.parserSingle(mpBs.sql(), ms.getId(), parameter, ms));
        }
    }

    protected void processSelect(Select select, int index, String sql, Object obj, Object parameter, MappedStatement ms) {
        SelectBody selectBody = select.getSelectBody();
        if (selectBody instanceof PlainSelect) {
            this.setWhere((PlainSelect) selectBody, (String) obj, selectBody, parameter, ms);
        } else if (selectBody instanceof SetOperationList setOperationList) {
            List<SelectBody> selectBodyList = setOperationList.getSelects();
            selectBodyList.forEach((s) -> {
                this.setWhere((PlainSelect) s, (String) obj, selectBody);
            });
        }
    }

    protected void setWhere(PlainSelect plainSelect, String whereSegment, SelectBody selectBody, Object parameter, MappedStatement ms) {
        if (this.getDataPermissionHandler() instanceof MultiDataPermissionHandler) {
            this.processPlainSelect(plainSelect, whereSegment);
        } else {
            Expression sqlSegment = null;
            if (this.getDataPermissionHandler() instanceof CustomDataPermissionHandler handler) {
                sqlSegment = handler.getSqlSegment(plainSelect.getWhere(), whereSegment, selectBody, parameter, ms);
            } else {
                sqlSegment = this.getDataPermissionHandler().getSqlSegment(plainSelect.getWhere(), whereSegment);
            }
            if (null != sqlSegment) plainSelect.setWhere(sqlSegment);
        }
    }

    protected void setWhere(PlainSelect plainSelect, String whereSegment, SelectBody selectBody) {
        this.setWhere(plainSelect, whereSegment, selectBody, null, null);
    }

    public String parserSingle(String sql, Object obj, Object parameter, MappedStatement ms) {
        if (this.logger.isDebugEnabled()) {
            this.logger.debug("original SQL: " + sql);
        }

        try {
            Statement statement = JsqlParserGlobal.parse(sql);
            return this.processParser(statement, 0, sql, obj, parameter, ms);
        } catch (JSQLParserException var4) {
            throw ExceptionUtils.mpe("Failed to process, Error SQL: %s", var4.getCause(), new Object[]{sql});
        }
    }

    protected String processParser(Statement statement, int index, String sql, Object obj, Object parameter, MappedStatement ms) {
        if (this.logger.isDebugEnabled()) {
            this.logger.debug("SQL to parse, SQL: " + sql);
        }

        if (statement instanceof Insert) {
            this.processInsert((Insert) statement, index, sql, obj);
        } else if (statement instanceof Select) {
            this.processSelect((Select) statement, index, sql, obj, parameter, ms);
        } else if (statement instanceof Update) {
            this.processUpdate((Update) statement, index, sql, obj);
        } else if (statement instanceof Delete) {
            this.processDelete((Delete) statement, index, sql, obj);
        }

        sql = statement.toString();
        if (this.logger.isDebugEnabled()) {
            this.logger.debug("parse the finished SQL: " + sql);
        }

        return sql;
    }
}
