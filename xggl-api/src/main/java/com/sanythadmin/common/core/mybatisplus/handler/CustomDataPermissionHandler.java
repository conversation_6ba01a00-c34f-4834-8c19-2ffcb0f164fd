package com.sanythadmin.common.core.mybatisplus.handler;

import com.baomidou.mybatisplus.extension.plugins.handler.DataPermissionHandler;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.statement.select.SelectBody;
import org.apache.ibatis.mapping.MappedStatement;

/**
 * Created by JIANGPING on 2024/8/21.
 */
public interface CustomDataPermissionHandler extends DataPermissionHandler {
    Expression getSqlSegment(Expression where, String mappedStatementId, SelectBody selectBody, Object parameter, MappedStatement ms);
}
