package com.sanythadmin.common.core.mybatisplus.interceptor;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ParameterUtils;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.parser.JsqlParserGlobal;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.sanythadmin.common.core.mybatisplus.base.MyPage;
import com.sanythadmin.common.core.utils.CommonUtil;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.select.*;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.util.StringUtils;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @since 2025/7/31.
 */
public class CustomPaginationInterceptor extends PaginationInnerInterceptor {

    @Override
    public boolean willDoQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
        IPage<?> page = ParameterUtils.findPage(parameter).orElse(null);
        if (page != null && page.getSize() >= 0L && page.searchCount() && resultHandler == Executor.NO_RESULT_HANDLER) {
            MappedStatement countMs = this.buildCountMappedStatement(ms, page.countId());
            BoundSql countSql;
            if (countMs != null) {
                countSql = countMs.getBoundSql(parameter);
            } else {
                countMs = this.buildAutoCountMappedStatement(ms);
                String countSqlStr = this.autoCountSql(page, boundSql.getSql());
                if (isJoinQuery(countSqlStr) && page instanceof MyPage<?> p
                        && StringUtils.hasText(p.getDistinctCountColumn()))
                    countSqlStr = generateDistinctCountSql(countSqlStr, p.getDistinctCountColumn());
                PluginUtils.MPBoundSql mpBoundSql = PluginUtils.mpBoundSql(boundSql);
                countSql = new BoundSql(countMs.getConfiguration(), countSqlStr, mpBoundSql.parameterMappings(), parameter);
                PluginUtils.setAdditionalParameter(countSql, mpBoundSql.additionalParameters());
            }

            CacheKey cacheKey = executor.createCacheKey(countMs, parameter, rowBounds, countSql);
            List<Object> result = executor.query(countMs, parameter, rowBounds, resultHandler, cacheKey, countSql);
            long total = 0L;
            if (CollectionUtils.isNotEmpty(result)) {
                Object o = result.get(0);
                if (o != null) {
                    total = Long.parseLong(o.toString());
                }
            }

            page.setTotal(total);
            return this.continuePage(page);
        } else {
            return true;
        }
    }

    protected String generateDistinctCountSql(String originalSql, String distinctCountColumn) {
        try {
            Select select = (Select) JsqlParserGlobal.parse(originalSql);
            PlainSelect innerSelect = (PlainSelect) (select).getSelectBody();
            List<SelectItem> selectItems = Arrays.stream(CommonUtil.split(distinctCountColumn))
                    .map(String::trim)
                    .map(Column::new)
                    .map(SelectExpressionItem::new)
                    .collect(Collectors.toList());

            innerSelect.setDistinct(new Distinct());
            innerSelect.setSelectItems(selectItems);

            innerSelect.setOrderByElements(null);
            innerSelect.setLimit(null);
            innerSelect.setOffset(null);

            PlainSelect outerSelect = new PlainSelect();
            outerSelect.setSelectItems(COUNT_SELECT_ITEM);

            SubSelect subSelect = new SubSelect();
            subSelect.setSelectBody(innerSelect);
            outerSelect.setFromItem(subSelect);
            return outerSelect.toString();
        } catch (JSQLParserException e) {
            this.logger.warn("Error occurred during construction of a DISTINCT COUNT SQL query, exception:\n" + e.getCause());
            return originalSql;
        }
    }

    protected boolean isJoinQuery(String sql) {
        return sql.toUpperCase().contains(" JOIN ");
    }
}
