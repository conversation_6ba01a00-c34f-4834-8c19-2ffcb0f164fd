package com.sanythadmin.common.core.mybatisplus.handler;

import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.sanythadmin.common.core.Constants;
import com.sanythadmin.common.core.annotation.DataPermission;
import com.sanythadmin.common.core.mybatisplus.base.MyMPQueryParams;
import com.sanythadmin.common.core.utils.CommonUtil;
import com.sanythadmin.common.core.utils.SecurityUtil;
import com.sanythadmin.common.core.utils.SpringContextUtil;
import com.sanythadmin.common.enums.UserType;
import com.sanythadmin.common.system.entity.SysAccount;
import com.sanythadmin.common.system.entity.SysRole;
import com.sanythadmin.project.userInfo.dto.DataScopeCache;
import com.sanythadmin.project.userInfo.entity.UserDataScope;
import com.sanythadmin.project.userInfo.service.UserDataScopeService;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.expression.operators.relational.ExistsExpression;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.select.*;
import net.sf.jsqlparser.util.cnfexpression.MultiAndExpression;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ResultMap;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * Created by JIANGPING on 2024/8/21.
 */
@Slf4j
public class CustomDataPermissionHandlerImpl implements CustomDataPermissionHandler {
    private static final ConcurrentHashMap<String, List<Method>> concurrentHashMap = new ConcurrentHashMap<>();

    @Override
    public Expression getSqlSegment(Expression where, String mappedStatementId) {
        return null;
    }

    @Override
    public Expression getSqlSegment(Expression where, String mappedStatementId, SelectBody selectBody, Object parameter, MappedStatement ms) {
        try {
            String className = mappedStatementId.substring(0, mappedStatementId.lastIndexOf("."));
            String methodName;
            String returnTypeName = null;
            if (mappedStatementId.contains("-")) {
                methodName = mappedStatementId.substring(mappedStatementId.lastIndexOf(".") + 1, mappedStatementId.indexOf("-"));
                returnTypeName = mappedStatementId.substring(mappedStatementId.indexOf("-") + 1).replaceAll("-", ".");
            } else {
                methodName = mappedStatementId.substring(mappedStatementId.lastIndexOf(".") + 1);
            }

            String alias = null;
            UserType userType = null;
            Class<?> aClass = null;
            if (parameter instanceof MapperMethod.ParamMap) {
                MapperMethod.ParamMap paramMap = (MapperMethod.ParamMap) parameter;
                // 从 paramMap 中获取传入的表别名
                MyMPQueryParams params = (MyMPQueryParams) paramMap.getOrDefault("params", new MyMPQueryParams());
                alias = params.getTableAlias();
                userType = params.getUserType();
                aClass = params.getAClass();
            }
            String suffix = "WithPermission";
            if (methodName.endsWith(suffix))
                return expressionList(where, selectBody, alias, userType, false, aClass);
            List<Method> usedDataPermissionList = getMethods(className);
            if (CollectionUtils.isEmpty(usedDataPermissionList))
                return where;

            int parameterNum = 0;
            if (!Objects.isNull(parameter)) {
                if (parameter instanceof MapperMethod.ParamMap) {
                    MapperMethod.ParamMap paramMap = (MapperMethod.ParamMap) parameter;
                    parameterNum = paramMap.keySet().stream().filter(s -> String.valueOf(s).startsWith("param")).toList().size();
                } else {
                    parameterNum = 1;
                }
            }

            if (!StringUtils.hasText(returnTypeName)) {
                List<ResultMap> resultMaps = ms.getResultMaps();
                if (!CollectionUtils.isEmpty(resultMaps))
                    returnTypeName = resultMaps.get(0).getType().getName();
            }

            for (Method m : usedDataPermissionList) {
                boolean match = isMatch(m, methodName, parameterNum, returnTypeName, ms);
                if (!match) continue;
                DataPermission annotation = m.getAnnotation(DataPermission.class);
                return expressionList(where, selectBody, annotation.alias(), userType, annotation.existsQuery(), aClass);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return where;
    }

    private static List<Method> getMethods(String className) throws ClassNotFoundException {
        List<Method> usedDataPermissionList = null;
        if (concurrentHashMap.containsKey(className)) {
            usedDataPermissionList = concurrentHashMap.get(className);
        } else {
            Method[] methods = Class.forName(className).getMethods();
            usedDataPermissionList = Arrays.stream(methods).filter(m ->
                    m.getDeclaringClass().toGenericString().endsWith(className) &&
                            m.getAnnotation(DataPermission.class) != null).toList();
            concurrentHashMap.put(className, usedDataPermissionList);
        }
        return usedDataPermissionList;
    }

    private boolean isMatch(Method m, String methodName, int parameterNum, String returnTypeName, MappedStatement ms) {
        int parameterCount = m.getParameterCount();
        Type genericReturnType = m.getGenericReturnType();
        if (Objects.equals(methodName, m.getName()))
            return parameterCount == parameterNum && genericReturnType.getTypeName().contains(returnTypeName);
        return false;
    }

    private Expression expressionList(Expression where, SelectBody selectBody, String alias, UserType userType, boolean existsQuery, Class<?> aClass) {
        if (!StringUtils.hasText(alias)) {
            if (selectBody instanceof PlainSelect plainSelect) {
                if (plainSelect.getFromItem() instanceof Table table) {
                    if (!CollectionUtils.isEmpty(plainSelect.getJoins()) && aClass != null) {
                        TableInfo tableInfo = TableInfoHelper.getTableInfo(aClass);
                        for (Join join : plainSelect.getJoins()) {
                            Table rightItem = (Table) join.getRightItem();
                            if (tableInfo.getTableName().equalsIgnoreCase(rightItem.getName())) {
                                alias = rightItem.getAlias().getName();
                                break;
                            }
                        }
                    } else {
                        alias = !Objects.isNull(table.getAlias()) ? table.getAlias().getName() : table.getName();
                    }
                }
            }
        }

        List<Expression> expressionList = expressionList(alias, userType, existsQuery, aClass);
        if (CollectionUtils.isEmpty(expressionList)) return where;
        int index = 0;
        if (Objects.isNull(where)) {
            where = expressionList.get(0);
            index++;
        }
        for (int i = index; i < expressionList.size(); i++) {
            where = new AndExpression(where, expressionList.get(i));
        }
        return where;
    }


    /**
     * 追加数据权限
     *
     * @param alias
     * @param existsQuery
     * @return
     */
    public List<Expression> expressionList(String alias, UserType userType, boolean existsQuery) {
        return expressionList(alias, userType, existsQuery, null);
    }


    /**
     * 追加数据权限
     *
     * @param alias
     * @param existsQuery
     * @return
     */
    public List<Expression> expressionList(String alias, UserType userType, boolean existsQuery, Class<?> aClass) {
        SysAccount account = SecurityUtil.getAccount();
        SysRole role = account.getRole();
        return expressionList(alias, userType, existsQuery, aClass, account.getUsername(), role.getId(), role.getRoleScope());
    }

    /**
     * 追加数据权限
     *
     * @param alias
     * @param existsQuery
     * @return
     */
    public List<Expression> expressionList(String alias, UserType userType, boolean existsQuery, Class<?> aClass,
                                           String username, String roleId, String roleScope) {
        if (!Constants.ROLE_SCOPE_QX.equals(roleScope)) {
            if (Objects.equals(Constants.ROLE_SCOPE_GR, roleScope)) {
                UserDataScope dataScope = new UserDataScope();
                dataScope.setXgh(username);
                return expressionList(dataScope, alias, userType, existsQuery, aClass);
            } else {
                UserDataScopeService dataScopeService = SpringContextUtil.getBean(UserDataScopeService.class);
                DataScopeCache dataScopeCache = dataScopeService.get(username, roleId);
                String class_id = dataScopeCache.getClassId();
                String dept_id = dataScopeCache.getDeptId();
                UserDataScope scope = dataScopeCache.getDataScope();
                if (Objects.equals(Constants.ROLE_SCOPE_BB, roleScope)) {
                    UserDataScope dataScope = new UserDataScope();
                    dataScope.setBjid(String.valueOf(class_id));
                    return expressionList(dataScope, alias, userType, existsQuery, aClass);
                } else if (Constants.ROLE_SCOPE_BJ.equals(roleScope)) {
                    if (!Objects.isNull(scope))
                        return expressionList(scope, alias, userType, existsQuery, aClass);
                    UserDataScope dataScope = new UserDataScope();
                    dataScope.setBjid(String.valueOf(class_id));
                    return expressionList(dataScope, alias, userType, existsQuery, aClass);
                } else if (Constants.ROLE_SCOPE_BY.equals(roleScope)) {
                    if (!Objects.isNull(scope))
                        return expressionList(scope, alias, userType, existsQuery, aClass);
                    UserDataScope dataScope = new UserDataScope();
                    dataScope.setXyid(String.valueOf(dept_id));
                    return expressionList(dataScope, alias, userType, existsQuery, aClass);
                }
            }
        }
        return null;
    }

    private List<Expression> expressionList(UserDataScope userDataScope, String alias, UserType userType, boolean existsQuery, Class<?> aClass) {
        List<Expression> list = new ArrayList<>();
        if (StringUtils.hasText(userDataScope.getXgh())) {
            String column = "XGH";
            List<String> stringList = Arrays.asList(CommonUtil.split(userDataScope.getXgh()));
            Expression expression = !Objects.isNull(aClass) ? newInExpression(stringList, alias, column) :
                    (existsQuery ? newExistsExpression(userDataScope, alias, column) :
                            newInExpression(stringList, alias, column));
            list.add(expression);
        } else {
            String[] fields = {"BJID", "ZYID", "XYID", "PYCCID", "NJID"};
            List<String> fieldList = Arrays.asList(fields);
            if (!Objects.isNull(aClass)) {
                Field[] allFields = CommonUtil.getAllFields(aClass);
                for (Field field : allFields) {
                    jakarta.persistence.Column annotation = field.getAnnotation(jakarta.persistence.Column.class);
                    if (annotation != null && StringUtils.hasText(annotation.name())
                            && fieldList.contains(annotation.name().toUpperCase())) {
                        Object value = CommonUtil.getValue(annotation.name().toLowerCase(), userDataScope);
                        if (!Objects.isNull(value) && StringUtils.hasLength(String.valueOf(value))) {
                            InExpression inExpression = newInExpression(Arrays.asList(CommonUtil.split(String.valueOf(value))), alias, annotation.name());
                            list.add(inExpression);
                        }
                    }
                }
            } else {
                PlainSelect subSelect = new PlainSelect();
                SelectItem selectItem = new SelectExpressionItem(new LongValue(1));
                subSelect.addSelectItems(selectItem);
//                Table userInfoTable = new Table("SYT_USER_INFO").withAlias(new Alias("SCOPE_U", false));
                Table userInfoTable = new Table("SYT_USER_ORG_MAP").withAlias(new Alias("SCOPE_U", false));
                subSelect.setFromItem(userInfoTable);
                List<Expression> subWhereConditions = new ArrayList<>();
//                if (userType != null) {
//                    EqualsTo userTypeCondition = new EqualsTo();
//                    userTypeCondition.setLeftExpression(new Column("SCOPE_U.user_type"));
//                    userTypeCondition.setRightExpression(new LongValue(userType.getValue()));
//                    subWhereConditions.add(userTypeCondition);
//                }
                for (String field : fields) {
                    Object value = CommonUtil.getValue(field.toLowerCase(), userDataScope);
                    if (!Objects.isNull(value) && StringUtils.hasLength(String.valueOf(value))) {
                        String[] split = CommonUtil.split(String.valueOf(value));
                        List<Expression> inValues = new ArrayList<>(Arrays.stream(split).map(StringValue::new).toList());
                        if (!inValues.isEmpty()) {
                            InExpression inExpression = new InExpression();
                            inExpression.setLeftExpression(new Column("SCOPE_U." + field));
                            ExpressionList expressionList = new ExpressionList(inValues);
                            inExpression.setRightItemsList(expressionList);
                            subWhereConditions.add(inExpression);
                        }
                    }

                }

                Expression combinedSubWhereCondition = subWhereConditions.get(0);
                for (int i = 1; i < subWhereConditions.size(); i++) {
                    combinedSubWhereCondition = new AndExpression(combinedSubWhereCondition, subWhereConditions.get(i));
                }
                EqualsTo xghCondition = new EqualsTo();
                xghCondition.setLeftExpression(buildColumn(alias, "XGH"));
                xghCondition.setRightExpression(buildColumn("SCOPE_U", "XGH"));
                combinedSubWhereCondition = new AndExpression(combinedSubWhereCondition, xghCondition);

                subSelect.setWhere(combinedSubWhereCondition);

                ExistsExpression existsExpression = new ExistsExpression();
                existsExpression.setRightExpression(buildColumn(alias, ""));
                SubSelect existsSubSelect = new SubSelect();
                existsSubSelect.setSelectBody(subSelect);
                existsExpression.setRightExpression(existsSubSelect);
                list.add(existsExpression);
            }
        }
        return list;
    }

    private static final List<SelectItem> SELECT_1_ITEM = Collections.singletonList(
            new SelectExpressionItem(new LongValue(1)));
    private static final Table table = new Table().withName("SYT_USER_DATA_SCOPE")
            .withAlias(new Alias("SCOPE", false));

    private List<Expression> equalsToList(UserDataScope userDataScope, String alias, String column) {
        String aliasColumn = StringUtils.hasLength(alias) ? alias + "." + column : column;
        return Arrays.asList(
                new EqualsTo(new Column(table, "GLZ_XGH"),
                        new StringValue(userDataScope.getGlzXgh())
                ),
                new EqualsTo(new Column(table, "ROLE_ID"),
                        new StringValue(userDataScope.getRoleId())
                ),
                new EqualsTo(new Column(table, column),
                        new Column(aliasColumn)
                )
        );
    }

    private ExistsExpression newExistsExpression(UserDataScope userDataScope, String alias, String column) {
        return new ExistsExpression().withRightExpression(new SubSelect().withSelectBody(
                new PlainSelect().withSelectItems(SELECT_1_ITEM).withFromItem(table)
                        .withWhere(new MultiAndExpression(equalsToList(userDataScope, alias, column)))));
    }

    private InExpression newInExpression(Collection<String> collection, String alias, String column) {
        column = StringUtils.hasLength(alias) ? alias + "." + column : column;
        return new InExpression(new Column(column), new ExpressionList(collection.stream()
                .map(StringValue::new).collect(Collectors.toList())));
    }

    public static Column buildColumn(String tableAlias, String columnName) {
        if (StringUtils.hasText(tableAlias))
            columnName = tableAlias + "." + columnName;
        return new Column(columnName);
    }
}
