package com.sanythadmin.common.core.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.sanythadmin.common.core.mybatisplus.handler.CustomDataPermissionHandlerImpl;
import com.sanythadmin.common.core.mybatisplus.interceptor.CustomDataPermissionInterceptor;
import com.sanythadmin.common.core.mybatisplus.interceptor.EncryptInterceptor;
import com.sanythadmin.common.core.mybatisplus.injector.MyMPJSqlInjector;
import com.sanythadmin.common.core.mybatisplus.interceptor.CustomPaginationInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MybatisPlus配置
 *
 * <AUTHOR>
 * @since 2018-02-22 11:29:28
 */
@Configuration
public class MybatisPlusConfig {

    @Bean
    public MyMPJSqlInjector myMPJSqlInjector() {
        return new MyMPJSqlInjector();
    }

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 创建动态表名插件，并添加到拦截器链中
        /*DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
        dynamicTableNameInnerInterceptor.setTableNameHandler((sql, tableName) -> {
            try {
                String dynamicTable = DynamicTableContext.getTableName();
                if (StrUtil.isNotBlank(dynamicTable)) {
                    log.info("dynamicTable：" + dynamicTable);
                    return dynamicTable;
                }
                return tableName;
            }finally {
//                DynamicTableContext.clear();
            }
        });
        interceptor.addInnerInterceptor(dynamicTableNameInnerInterceptor);*/

        // 加解密拦截器
        interceptor.addInnerInterceptor(new EncryptInterceptor());
        // 数据权限拦截器
//        interceptor.addInnerInterceptor(new DataPermissionInterceptor(new MyDataPermissionHandler()));
        interceptor.addInnerInterceptor(new CustomDataPermissionInterceptor(new CustomDataPermissionHandlerImpl()));
        // 分页插件配置
        interceptor.addInnerInterceptor(new CustomPaginationInterceptor());

        return interceptor;
    }

}
