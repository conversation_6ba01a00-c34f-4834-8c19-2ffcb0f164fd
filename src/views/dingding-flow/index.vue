<template>
  <ele-page hide-footer flex-table>
    <search @search="reload" @handleClick="handleClickEvent" />
    <!--    <search @search="reload" @handleClick="handleClickEvent"/>-->
    <ele-card flex-table :body-style="{ padding: '0 8px 10px 8px!important' }">
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        :border="true"
        tooltip-effect="light"
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <el-button size="small" class="ele-btn-icon" @click="openEdit()">
            新建
          </el-button>
          <el-button size="small" class="ele-btn-icon" @click="remove()">
            删除
          </el-button>
        </template>
        <template #name="{ row }">
          <ele-tooltip content="点我编辑" placement="left" effect="light">
            <el-link type="primary" underline="never" @click="openEdit(row)">
              {{ row.name }}
            </el-link>
          </ele-tooltip>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ElNotification } from 'element-plus/es';
  import { computed, ref, unref, watch } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { ElLoading, ElMessage as EleMessage } from 'element-plus';
  import search from './components/search.vue';
  import { queryPage, removes } from './api/index.js';
  import { useRouter } from 'vue-router';
  import { usePageTab } from '@/utils/use-page-tab';
  import { mapMutations, mapState } from '@/plugins/lib.js';

  const { removePageTab, getRouteTabKey, addPageTab, setPageTabTitle } =
    usePageTab();

  let { setSharedWorkFlowData } = mapMutations();
  const { currentRoute, push } = useRouter();

  // 路由参数处理
  const moduleCode = ref('');
  const routeType = ref('');

  watch(
    currentRoute,
    (route) => {
      const { path, params: routeParams, query: routeQuery } = unref(route);
      const pathSegments = path.split('/').filter(segment => segment !== '');
      if (pathSegments.length >= 2) {
        moduleCode.value = pathSegments[1] || '';
        routeType.value = pathSegments[2] || pathSegments[1] || '';
      } else {
        moduleCode.value = routeParams?.moduleCode || routeQuery?.moduleCode || '';
        routeType.value = routeParams?.routeType || routeQuery?.routeType || pathSegments[2] || '';
      }
    },
    { immediate: true }
  );

  const validatedModuleCode = computed(() => {
    const code = moduleCode.value;
    if (typeof code === 'string' && code.trim().length > 0) {
      return code.trim();
    }
    return '';
  });

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left',
      reserveSelection: true
    },
    {
      prop: 'name',
      slot: 'name',
      label: '流程名称',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '添加时间'
    }
  ]);

  /** 列表选中数据 */
  const selections = ref([]);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      type: routeType.value, // 使用响应式的 routeType
      moduleCode: validatedModuleCode.value // 使用验证后的 moduleCode 参数
    });
  };

  const handleClickEvent = (type) => {
    console.log(type);
    if (type === 'add') openEdit();
  };

  const openEdit = (row) => {
    let json = {
      workflowId: row?.id,
      workflowName: row?.name + '-',
      // 添加当前路由参数，供详情页返回时使用
      moduleCode: validatedModuleCode.value,
      routeType: routeType.value
    };
    setSharedWorkFlowData(json);
    push({
      path: '/dingding-flow/details'
    });
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      // EleMessage.error('请至少选择一条数据');
      ElNotification({
        title: '系统提示',
        message: '请至少选择一条数据 ',
        type: 'warning',
        duration: 2500,
        position: 'top-right'
      });
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.xmmc).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = ElLoading.service({
          lock: true,
          text: '请求中...',
          background: 'rgba(0, 0, 0, 0.1)'
        });
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef.value?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef.value?.reload?.();
    }
  };
</script>

<script>
  export default {
    name: 'DINGDINGFLOWINDEX'
  };
</script>
