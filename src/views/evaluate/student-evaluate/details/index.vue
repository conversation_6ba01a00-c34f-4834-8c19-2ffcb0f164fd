<!-- 编辑弹窗 -->
<template>
  <ele-drawer
    :size="600"
    :title="`【${data.cpnf}年份】评分详情`"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <div class="student-header">
      <!-- 3D头像区域 -->
      <div class="avatar-section">
        <div class="avatar-3d">
          <!-- 照片显示 -->
          <img
            v-if="showAvatarPhoto"
            :src="avatarPhotoUrl"
            alt="学生头像"
            class="avatar-img"
            :style="avatarStyle"
            @load="handleImageLoad"
            @error="handleImageError"
          />
          <template v-else-if="currentData">
            <img
              v-if="currentData.xb === '男'"
              src="/male.png"
              :alt="currentData.xb"
              class="avatar-img"
            />
            <img
              v-else-if="currentData.xb === '女'"
              src="/female.png"
              :alt="currentData.xb"
              class="avatar-img"
            />
          </template>
          <!--  加载状态-->
          <el-skeleton
            v-else
            class="avatar-loading"
            :style="{ width: `${avatarSize}px`, height: `${avatarSize}px` }"
            variant="circle"
          />
          <!-- 光晕效果 -->
          <div class="avatar-halo"></div>
        </div>
      </div>

      <!-- 学生基本信息区域 -->
      <div class="info-section">
        <!-- 姓名和学号行 -->
        <div class="student-name-row">
          <!-- <h3 class="meta-info">欢迎回来，</h3> -->
          <h3 class="student-name">
            <span class="highlight">{{ currentData.xm || '--' }}</span>
          </h3>
          <div class="student-meta">
            <span class="student-id">{{ currentData.xgh || '--' }}</span>
          </div>
        </div>

        <!-- 学院专业班级信息 -->
        <div class="meta-info">
          <span>
            {{ currentData.xymc || '--' }} · {{ currentData.zymc || '--' }} ·
            {{ currentData.njmc || '--' }} ·
            {{ currentData.bjmc || '--' }}
          </span>
        </div>
        <!-- <div class="meta-info">
          <span>
            {{ data.cpnf }}
          </span>
        </div> -->
        <!-- 辅导员信息 -->
        <!-- <div class="counselors" v-if="currentCounselors.length > 0">
          <div
            class="counselor-item"
            v-for="(counselor, index) in currentCounselors"
            :key="index"
          >
            <el-tooltip
              effect="light"
              :content="`点击拨打 ${counselor.sjh}`"
              placement="right-start"
            >
              <el-tag
                class="counselor-tag"
                @click="callCounselor(counselor.sjh)"
              >
                {{ counselor.roleName }} {{ counselor.xm }} {{ counselor.sjh }}
                <el-icon>
                  <Phone />
                </el-icon>
              </el-tag>
            </el-tooltip>
          </div>
        </div> -->
        <!-- <p>今天是{{ currentDate }}</p> -->
      </div>

      <!-- 标签云 -->
      <!-- <div class="tag-cloud">
        <span
          v-for="(tag, index) in tags"
          :key="index"
          class="tag"
          :style="{
            transform: `rotate(${getRandomRotation()}deg)`,
            backgroundColor: tagColors[index % tagColors.length],
            fontSize: `${getRandomSize()}px`
          }"
          @click="handleTagClick(tag)"
        >
          {{ tag }}
        </span>
      </div> -->
    </div>
    <el-row :gutter="8">
      <el-col :xs="12" :sm="12" :md="12" :lg="12">
        <el-card class="stat-card" :body-style="{ padding: '5px' }">
          <div class="stat-content">
            <!-- <div class="stat-value">{{ studentData.gpa }}</div> -->
            <div class="stat-value">{{ data?.xshpf }}</div>
            <div class="stat-label">本班学生互评平均分</div>
            <div class="stat-desc">
              <div> 满分: 5 分 </div>
              <!-- <div> 最高分: 5 分 </div>
              <div> 最低分: 5 分 </div> -->
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="12" :sm="12" :md="12" :lg="12">
        <el-card class="stat-card" :body-style="{ padding: '5px' }">
          <div class="stat-content">
            <!-- <div class="stat-value">{{ studentData.gpa }}</div> -->
            <div class="stat-value">{{ data?.xypf }}</div>
            <div class="stat-label">学院评分</div>
            <div class="stat-desc">
              <!-- <el-progress
                :percentage="1"
                :stroke-width="6"
                :show-text="false"
                :color="'red'"
              /> -->
              满分: 5分
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!-- 主要内容区 -->
    <div class="main-content">
      <el-card class="data-card">
        <template #header>
          <div class="card-header">
            <span class="card-title">本班学生互评详情</span>
            <!-- <div class="card-actions">
            <el-tag type="info">学年评定</el-tag>
            <el-button size="small" type="text">详情</el-button>
          </div> -->
          </div>
        </template>
        <!-- <EvaluationTable :data="evaluationData" /> -->
        <ele-pro-table
          ref="tableRef"
          row-key="id"
          :columns="columns"
          :datasource="datasource"
          :loadOnCreated="true"
          :border="true"
          :pagination="false"
          :toolbar="false"
          :show-overflow-tooltip="true"
          highlight-current-row
          tooltip-effect="light"
          :footer-style="{ paddingBottom: '3px' }"
          style="padding-bottom: 0"
        />
        <!-- v-model:selections="selections" -->
      </el-card>
    </div>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">返回</el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, reactive, watch, onMounted, computed } from 'vue';
  import {
    getPersonInfo,
    getPersonInfoData,
    getStuPersonAdvisor
  } from '@/views/personInfo/st/api/index.js';
  import { getEvaluatePeerReviewRecordPage } from '../api/index.js';
  import { ElMessage, ElSkeleton } from 'element-plus';
  import { usePageTab } from '@/utils/use-page-tab.js';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    data: Object,
    currentXgh: {
      // 当前学工号
      type: String,
      required: true
    },
    routeType: {
      // 路由类型
      type: String,
      required: true
    }
  });
  const gpaPercentage = ref(50);
  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (newVal) => {
      console.log('newVal :>> ', newVal);
      if (newVal) {
        queryPersonInfo();
        updateCurrentDate();
        reload({ page: 1, limit: 100 });
      }
    }
  );

  const { setPageTabTitle } = usePageTab();

  // 响应式数据
  const currentData = ref({}); // 当前学生数据
  const currentCounselors = ref([]); // 辅导员列表
  const imageLoaded = ref(false); // 图片是否加载完成
  const imageError = ref(false); // 图片加载是否出错
  const loading = ref(true); // 数据加载状态
  const mousePosition = ref({ x: 0, y: 0 }); // 鼠标位置
  const tableRef = ref(null);
  const limit = ref(200);
  // 当前日期
  const currentDate = ref('');
  const updateCurrentDate = () => {
    const now = new Date();
    const options = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    };
    currentDate.value = now.toLocaleDateString('zh-CN', options);
  };

  // 头像大小配置
  const avatarSize = ref(120); // 默认80px，可根据需要调整

  // 图片处理逻辑
  const showAvatarPhoto = computed(() => {
    return imageLoaded.value && currentData.value?.photo && !imageError.value;
  });

  const avatarPhotoUrl = computed(() => {
    try {
      const photo = JSON.parse(currentData.value.photo);
      return photo[0]?.id ? `/api/file/inline/${photo[0].id}` : '';
    } catch {
      return '';
    }
  });

  const avatarStyle = computed(() => {
    const rotateX = (mousePosition.value.y - 0.5) * 20;
    const rotateY = -(mousePosition.value.x - 0.5) * 20;
    return {
      transform: `rotateX(${rotateX}deg) rotateY(${rotateY}deg)`
    };
  });

  /** 表格列配置 */
  const columns = ref([
    // {
    //   prop: 'cpnf',
    //   label: '测评年份'
    // },
    // { prop: 'xgh', label: '学号' },
    // { prop: 'xm', label: '姓名' },
    { prop: 'order', label: '序号', width: 60, align: 'center' },
    { prop: 'raterAccount', label: '评分者学号' },
    { prop: 'rater', label: '评分者' },
    // { prop: 'pyccmc', label: '培养层次' },
    { prop: 'score', label: '评分' }
  ]);

  /** 表格数据源 */
  const datasource = async ({ page, limit, where, orders, filters }) => {
    const res = await getEvaluatePeerReviewRecordPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit: 100,
      configId: props.data.configId,
      xgh: props.data.xgh
    });
    res.list.sort((a, b) => {
      return b.score - a.score;
    });
    res.list.map((item, index) => {
      item.order = index + 1;
    });
    // res.list.map((item) => {
    //   if (item.scoreDetail) {
    //     let scoreDetail = JSON.parse(item.scoreDetail);
    //     Object.entries(scoreDetail).map(([key, value]) => {
    //       item[key] = value;
    //     });
    //   }
    // });
    return {
      list: res.list,
      count: res.count
    };
  };

  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  const handleImageLoad = () => {
    imageLoaded.value = true;
    imageError.value = false;
  };

  const handleImageError = () => {
    imageError.value = true;
  };

  // 数据获取方法
  const queryPersonInfo = async () => {
    try {
      loading.value = true;
      const data = await getPersonInfo('student', props.data.xgh);

      currentData.value = data;
      // setPageTabTitle(`${data.xm}-学生画像查看`);

      // 如果有照片则尝试加载
      if (data.photo) {
        imageLoaded.value = true;
        imageError.value = false;
      }
    } catch (e) {
      ElMessage.error(e.message || '获取学生信息失败');
    } finally {
      await initStuPersonAdvisor();
      loading.value = false;
    }
  };

  const initStuPersonAdvisor = async () => {
    try {
      // console.log(currentData.value);
      currentCounselors.value = await getStuPersonAdvisor(props.data.xgh);
    } catch (e) {
      ElMessage.error(e.message || '获取辅导员信息失败');
    }
  };

  // 监听学工号变化
  // watch(
  //   () => props.data,
  //   (val) => {
  //     console.log('props.data :>> ', val);
  //     if (val) {
  //       queryPersonInfo();
  //     }
  //   },
  //   { immediate: true }
  // );
  // 生命周期钩子
  // onMounted(() => {
  //   updateCurrentDate();
  // });
</script>
<style scoped>
  .student-header {
    display: flex;
    align-items: center;
    gap: 30px;
    padding: 6px 3px 0 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
  }

  /* 头像容器 */
  .avatar-section {
    position: relative;
    flex-shrink: 0;
  }

  .avatar-3d {
    width: v-bind('avatarSize + "px"');
    height: v-bind('avatarSize + "px"');
    /* perspective: 1000px; */
    position: relative;
  }

  /* 头像图片样式 */
  .avatar-img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid white;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.1s ease-out;
    background: #f5f7fa;
  }

  /* 默认头像样式 */
  .gender-fallback-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f2f5;
    border-radius: 50%;
    border: 3px solid white;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .icon-svg {
    color: #606266;
  }

  /* 加载状态 */
  .avatar-loading {
    border-radius: 50%;
  }

  /* 光晕效果 */
  .avatar-halo {
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    background: radial-gradient(
      circle at center,
      rgba(64, 158, 255, 0.2) 0%,
      transparent 70%
    );
    pointer-events: none;
  }

  /* 响应式调整 */
  @media (max-width: 768px) {
    .avatar-section {
      --avatar-size: 60px;
    }
  }

  /* 信息区域样式 */
  .info-section {
    flex: 1;
    min-width: 0;
    margin: 5px;
  }

  .student-name-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 6px;
  }

  .student-name {
    margin: 0;
    font-size: 24px;
    font-weight: bold;
    color: #1e293b;
  }

  .highlight {
    color: #409eff;
  }

  .student-meta {
    font-size: 14px;
    color: #64748b;
  }

  .meta-info {
    margin: 10px 0;
    color: #606266;
    font-size: 14px;
  }
  .stat-card {
    border-radius: 12px;
    transition: all 0.3s;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    margin-bottom: 0 !important;
    height: 100%;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1);
    }
  }

  .stat-content {
    padding: 10px;
    text-align: center;
    display: flex;
    flex-direction: column;
    height: 100%;

    .stat-value {
      font-size: 24px;
      font-weight: bold;
      /* margin-bottom: 5px; */
      color: var(--el-color-primary);
    }

    .stat-label {
      font-size: 16px;
      color: var(--el-text-color-primary);
      margin-bottom: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .stat-desc {
      font-size: 12px;
      color: var(--el-text-color-secondary);
      margin-top: auto;
      display: flex;
      flex-direction: column;
      align-items: center;

      :deep(.el-progress) {
        width: 80%;
        margin-bottom: 5px;
      }

      .warning-tag {
        margin: 2px;
      }

      .mini-chart {
        width: 80px;
        height: 30px;
        margin: 5px auto;
      }
    }
  }

  .main-content {
    /* display: flex;
    flex-wrap: wrap; */
    /* gap: 8px;
    width: 100%; */
    margin-top: 12px;

    /* @media (max-width: 1200px) {
      flex-direction: column;
    } */
  }
  /* 表格样式 */
  .data-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    margin-bottom: 0 !important;

    &:deep(.el-card__header) {
      padding: 12px 16px;
      border-bottom: 1px solid var(--el-border-color-light);
    }

    &:deep(.el-card__body) {
      padding: 16px;
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .card-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .card-actions {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
</style>
